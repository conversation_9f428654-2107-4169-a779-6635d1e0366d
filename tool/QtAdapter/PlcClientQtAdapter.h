#pragma once

#include <QObject>

/**
 * PlcClient 库的Qt适配器
 */
class PlcClientQtAdapter final : public QObject {
    Q_OBJECT
public:

    PlcClientQtAdapter(const PlcClientQtAdapter&) = delete;
    PlcClientQtAdapter& operator=(const PlcClientQtAdapter&) = delete;
    PlcClientQtAdapter(PlcClientQtAdapter&&) = delete;
    PlcClientQtAdapter& operator=(PlcClientQtAdapter&&) = delete;

    static PlcClientQtAdapter& getInstance();

public:

    /**
     * PlcClient 配置
     */
    struct Configure {

        /** Modbus 连接 */
        struct ModbusChannel {
            /** IP地址 */
            QString ip {};
            /** 端口号 */
            quint16 port = 502;
        };
        QVector<ModbusChannel> modbusChannels;

        /** 系统 */
        struct System {
            /** Modbus 连接 */
            qsizetype modbusChannelIndex {};

            /** 系统A 基地址 */
            quint16 systemAAddress {};
            /** 系统B 基地址 */
            quint16 systemBAddress {};

            /** 产品编号 */
            QString productCode {};
        } system;

        /** 激光测距工位 */
        struct WorkstationLaserRanging {
            /** Modbus 连接 */
            qsizetype modbusChannelIndex {};

            /** 工位控制A 基地址 */
            quint16 workstationControlAAddress {};
            /** 工位控制B 基地址 */
            quint16 workstationControlBAddress {};
            /** 状态A 基地址 */
            quint16 statusAAddress {};
            /** 点位B 基地址 */
            quint16 positionBAddress {};

            /** 水平点位数量 */
            quint16 horizontalPositionSize {};

            /** 激光测距的基准值 */
            float referenceValue {};
            /** 激光测距的下限值 */
            float lowerLimitValue {};
            /** 激光测距的上限值 */
            float upperLimitValue {};

            /** 传感器数量 */
            enum class SensorSize {
                /** 单传感器 */
                ONE = 1,
                /** 双传感器 */
                TWO = 2,
            } sensorSize = SensorSize::ONE;
        };
        QVector<WorkstationLaserRanging> workstationLaserRangings;

        /** 2D工位 */
        struct Workstation2D {
            /** Modbus 连接 */
            qsizetype modbusChannelIndex {};

            /** 工位控制A 基地址 */
            quint16 workstationControlAAddress {};
            /** 工位控制B 基地址 */
            quint16 workstationControlBAddress {};
            /** 点位组B 基地址 */
            quint16 positionGroupBAddress {};

            /** 水平点位数量 */
            quint16 horizontalPositionSize {};

            /** 点位组 */
            struct PositionGroup {
                /** 光源选择 */
                quint16 lightMask {};
                /** 光源坐标 */
                std::array<float, 16> lightCoordinates {};
                /** 点位 */
                struct Position {
                    /** 保持时长(ms) */
                    quint16 holdDuration {};
                    /** 相机选择 */
                    quint16 cameraMask {};
                    /** 相机坐标 */
                    std::array<float, 16> cameraCoordinates {};
                    /** 相机通道类型 */
                    enum class CameraChannelType {
                        /** 不属于任何通道 */
                        NONE = 0,
                        /** 通道1 */
                        ONE = 1,
                        /** 通道2 */
                        TWO = 2,
                    };
                    std::array<CameraChannelType, 16> cameraChannelTypes {};
                };
                QVector<Position> positions;
            };
            QVector<PositionGroup> positionGroups;
        };
        QVector<Workstation2D> workstation2Ds;

        /** 3D工位 */
        struct Workstation3D {
            /** Modbus 连接 */
            qsizetype modbusChannelIndex {};

            /** 工位控制A 基地址 */
            quint16 workstationControlAAddress {};
            /** 工位控制B 基地址 */
            quint16 workstationControlBAddress {};
            /** 点位B 基地址 */
            quint16 positionBAddress {};

            /** 水平点位数量 */
            uint16_t horizontalPositionSize {};

            /** 点位 */
            struct Position {
                /** Z轴选择 */
                quint16 zAxisMask {};
                /** Z轴坐标 */
                std::array<float, 16> zAxisCoordinates {};
            };
            QVector<Position> positions;
        };
        QVector<Workstation3D> workstation3Ds;

        /** 下料工位 */
        struct WorkstationUnload {
            /** Modbus 连接 */
            qsizetype modbusChannelIndex {};

            /** 工位控制A 基地址 */
            quint16 workstationControlAAddress {};
            /** 工位控制B 基地址 */
            quint16 workstationControlBAddress {};
            /** 分拣结果A 基地址 */
            quint16 sortResultAAddress {};
            /** 判定结果B 基地址 */
            quint16 resultBAddress {};
        };
        QVector<WorkstationUnload> workstationUnloads;

        /** 报警 */
        struct Alarm {
            /** Modbus 连接 */
            qsizetype modbusChannelIndex {};

            /** 报警信息A */
            quint16 alarmInfoAAddress {};
        } alarm;

        /**
         * 从 JSON 文件加载配置
         * @param fileName JSON文件路径
         * @return Configure 实例
         */
        static Configure fromJson(const QString& fileName);

        /**
         * 转换为字符串
         * @return 结果
         */
        [[nodiscard]] std::string toString() const;

    private:
        template<size_t N>
        static std::array<float, N> jsonArrayToFloatArray(const QJsonArray& jsonArray);
    };

    /** 运行状态 */
    enum class RunningState {
        /** 运行 */
        RUNNING = 100,
        /** 停止 */
        STOPPED = 200,
        /** 暂停 */
        PAUSED = 300,
    };

    enum class LogType {
        /** 调试信息 */
        DEBUG = 0,
        /** 一般信息 */
        INFO,
        /** 错误 */
        ERR,
    };

    /**
     * 分拣结果
     */
    class SortResult {
    public:
        /**
         * 托盘类型 1:OK 2:疑似NG 3:NG
         */
        quint16 trayType {};

        /** 托盘编号（从1开始） */
        quint16 trayIndex {};

        /** 穴位编号（从1开始） */
        quint16 holeIndex {};
    };

    /**
     * 启动
     * @param configure 配置
     * @return 成功返回 true，失败返回 false
     */
    bool start(const Configure &configure);

    /**
     * 停止
     */
    void stop();

    /**
     * 获取库的版本号
     * @return 版本号
     */
    QString getVersion();

signals:

    /**
     * Modbus 通信状态改变
     * @param modbusChannelIndex Modbus连接的序号
     * @param connected true: 已连接  false: 已断开
     */
    void modbusConnectionStateChanged(qsizetype modbusChannelIndex, bool connected);

    /**
     * 运行状态改变
     * @param runningState 运行状态
     */
    void runningStateChanged(const RunningState &runningState);

    /**
     * 状态字改变
     * @param stateWord 状态字
     */
    void stateWordChanged(quint16 stateWord);

    /**
     * 报警状态改变
     * @param alarms 报警状态
     */
    void alarmStateChanged(const std::array<bool, 160> &alarms);

    /**
     * 日志
     * @param logType 日志类型
     * @param log 日志内容
     */
    void log(const LogType &logType, const QString &log);
    
signals:

    /**
     * 2D工位 工位开始
     * @param workstation2DIndex 当前工位在2D工位中的序号
     * @param trayCode 托盘号
     */
    void workstationStarted2D(qsizetype workstation2DIndex,
                              const QString &trayCode);

    /**
     * 2D工位 点位组开始
     * @param workstation2DIndex 当前工位在2D工位中的序号
     * @param horizontalPositionIndex 水平点位序号
     * @param positionGroupIndex 点位组的序号
     */
    void positionGroupStarted2D(qsizetype workstation2DIndex,
                                qsizetype horizontalPositionIndex,
                                qsizetype positionGroupIndex);

    /**
     * 2D工位 查询点位组的收图结果
     * @param workstation2DIndex 当前工位在2D工位中的序号
     * @param horizontalPositionIndex 水平点位序号
     * @param positionGroupIndex 点位组的序号
     * @param result 收图成功返回true，失败返回false
     */
    void queryPositionGroupResult2D(qsizetype workstation2DIndex,
                                    qsizetype horizontalPositionIndex,
                                    qsizetype positionGroupIndex,
                                    bool &result);

    /**
     * 2D工位 工位结束
     * @param workstation2DIndex 当前工位在2D工位中的序号
     */
    void workstationFinished2D(qsizetype workstation2DIndex);

signals:

    /**
     * 3D工位 工位开始
     * @param workstation3DIndex 当前工位在3D工位中的序号
     * @param trayCode 托盘号
     */
    void workstationStarted3D(qsizetype workstation3DIndex,
                              const QString &trayCode);

    /**
     * 3D工位 点位开始
     * @param workstation3DIndex 当前工位在3D工位中的序号
     * @param horizontalPositionIndex 水平点位序号。<0时表示当前工位结束。
     * @param positionIndex 光学面序号
     */
    void positionStarted3D(qsizetype workstation3DIndex,
                           qsizetype &horizontalPositionIndex,
                           qsizetype &positionIndex);

    /**
     * 3D工位 点位到达。点位结束时返回
     * @param workstation3DIndex 当前工位在3D工位中的序号
     * @param horizontalPositionIndex 水平点位序号
     * @param positionIndex 光学面序号
     */
    void positionArrived3D(qsizetype workstation3DIndex,
                           qsizetype horizontalPositionIndex,
                           qsizetype positionIndex);

    /**
     * 3D工位 工位结束
     * @param workstation3DIndex 当前工位在3D工位中的序号
     */
    void workstationFinished3D(qsizetype workstation3DIndex);

signals:

    /**
     * 下料工位 查询判定结果
     * @param workstationUnloadIndex 当前工位在下料工位中的序号
     * @param trayCode 托盘号
     * @param result 判定结果
     */
    void queryResult(qsizetype workstationUnloadIndex,
                     const QString &trayCode,
                     std::array<quint8, 400> &result,
                     std::uint16_t &mould);

    /**
     * 上传分拣结果
     * @param workstationUnloadIndex 当前工位在下料工位中的序号
     * @param trayCode 托盘号
     * @param sortResults 分拣结果
     * @param result 上传结果
     */
    void sortResult(qsizetype workstationUnloadIndex,
                    const QString &trayCode,
                    QVector<SortResult> &sortResults,
                    bool &result);

private:

    PlcClientQtAdapter() = default;
    ~PlcClientQtAdapter() override = default;
};
