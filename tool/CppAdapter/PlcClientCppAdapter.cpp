#include "PlcClientCppAdapter.h"
#include "PlcClient.h"

#include <mutex>
#include <algorithm>
#include <fstream>
#include <sstream>

PlcClientCppAdapter& PlcClientCppAdapter::getInstance() {
    static PlcClientCppAdapter instance;

    static std::once_flag initialized;
    std::call_once(initialized, []
    {
        plcclient_setLogCallback([](const PlcClientLogType logType, const char *log) {
            instance.log(static_cast<LogType>(logType), log);
        });

        plcclient_setModbusConnectionStateChangedCallback([](const size_t modbusChannelIndex, const bool connected) {
            instance.modbusConnectionStateChanged(modbusChannelIndex, connected);
        });

        plcclient_setRunningStateChangedCallback([](const PlcClientRunningState runningState) {
            instance.runningStateChanged(static_cast<RunningState>(runningState));
        });

        plcclient_setStateWordChangedCallback([](const uint16_t stateWord) {
            instance.stateWordChanged(stateWord);
        });

        plcclient_setAlarmStateChangedCallback([](const bool *alarmState) {
            std::array<bool, 160> alarms{};
            std::copy_n(alarmState, alarms.size(), alarms.begin());
            instance.alarmStateChanged(alarms);
        });

        plcclient_setWorkstationStarted2DCallback([](const size_t workstation2DIndex, const char *trayCode) {
            instance.workstationStarted2D(workstation2DIndex, trayCode);
        });

        plcclient_setPositionGroupStarted2DCallback([](const size_t workstation2DIndex, const size_t horizontalPositionIndex, const size_t positionGroupIndex) {
            instance.positionGroupStarted2D(workstation2DIndex, horizontalPositionIndex, positionGroupIndex);
        });

        plcclient_setQueryPositionGroupResult2DCallback([](const size_t workstation2DIndex, const size_t horizontalPositionIndex, const size_t positionGroupIndex, bool &result) {
            instance.queryPositionGroupResult2D(workstation2DIndex, horizontalPositionIndex, positionGroupIndex, result);
        });

        plcclient_setWorkstationFinished2DCallback([](const size_t workstation2DIndex) {
            instance.workstationFinished2D(workstation2DIndex);
        });

        plcclient_setWorkstationStarted3DCallback([](const size_t workstation3DIndex, const char *trayCode) {
            instance.workstationStarted3D(workstation3DIndex, trayCode);
        });

        plcclient_setPositionStarted3DCallback([](const size_t workstation3DIndex, ptrdiff_t &horizontalPositionIndex, size_t &positionIndex) {
            instance.positionStarted3D(workstation3DIndex, horizontalPositionIndex, positionIndex);
        });

        plcclient_setPositionArrived3DCallback([](const size_t workstation3DIndex, const size_t horizontalPositionIndex, const size_t positionIndex) {
            instance.positionArrived3D(workstation3DIndex, horizontalPositionIndex, positionIndex);
        });

        plcclient_setWorkstationFinished3DCallback([](const size_t workstation3DIndex) {
            instance.workstationFinished3D(workstation3DIndex);
        });

        plcclient_setWorkstationUnloadQueryResultCallback([](const size_t workstationUnloadIndex, const char *trayCode, uint8_t *result, uint16_t *mould) {
            std::array<std::uint8_t, 400> r{};
            instance.queryResult(workstationUnloadIndex, trayCode, r, *mould);
            std::ranges::copy(r, result);
        });

        plcclient_setWorkstationUnloadSortResultCallback([](const size_t workstationUnloadIndex, const char *trayCode, const ::SortResult *sortResult, const size_t sortResultSize, bool &result) {
            std::vector<SortResult> sortResults {};
            for (size_t i{};i<sortResultSize;i++) {
                sortResults.push_back({sortResult[i].trayType, sortResult[i].trayIndex, sortResult[i].holeIndex});
            }
            instance.sortResult(workstationUnloadIndex, trayCode, sortResults, result);
        });
    });

    return instance;
}

PlcClientCppAdapter::Configure PlcClientCppAdapter::Configure::fromJson(const std::string& fileName) {
    std::ifstream file(fileName, std::ios::binary);
    if (!file.is_open()) {
        throw std::runtime_error("打开文件失败: " + fileName);
    }

    std::string content(
        (std::istreambuf_iterator<char>(file)),
        std::istreambuf_iterator<char>()
    );
    file.close();

    try {
        return nlohmann::json::parse(content).get<Configure>();
    }
    catch (const nlohmann::json::parse_error& e) {
        throw std::runtime_error("[语法错误] " + std::string(e.what()) + " 错误位置:"+ std::to_string(e.byte));
    }
    catch (const nlohmann::json::type_error& e) {
        throw std::runtime_error("[类型错误] 字段类型不匹配:" + std::string(e.what()) + " 预期类型:" + std::to_string(e.id));
    }
    catch (const nlohmann::json::out_of_range& e) {
        throw std::runtime_error("[字段错误] 缺失或越界:" +  std::string(e.what()));
    }
    catch (const nlohmann::json::exception& e) {
        throw std::runtime_error("[配置转换错误] 其他异常:" + std::string(e.what()));
    }
}

bool PlcClientCppAdapter::start(const Configure &configure) {
    ::PlcClientConfigure plcClientConfigure {};

    // Modbus 连接
    struct ModbusChannelData {
        std::string ip;
    };
    std::vector<ModbusChannelData> modbusChannelDatas;
    std::vector<::PlcClientConfigure::ModbusChannel> modbusChannels;

    modbusChannelDatas.reserve(configure.modbusChannels.size());
    modbusChannels.reserve(configure.modbusChannels.size());

    for (const auto &[
        ip,
        port
        ] : configure.modbusChannels) {
        modbusChannelDatas.push_back({ip});
        modbusChannels.push_back({modbusChannelDatas.back().ip.c_str(), port});
    }
    plcClientConfigure.modbusChannels = modbusChannels.data();
    plcClientConfigure.modbusChannelsSize = modbusChannels.size();

    // 系统
    struct System {
        std::string productCode;
    } system {
        configure.system.productCode
    };

    plcClientConfigure.system = {
        configure.system.modbusChannelIndex,
        configure.system.systemAAddress,
        configure.system.systemBAddress,
        system.productCode.data()
    };

    // 激光测距工位
    struct WorkstationLaserRangingData {
    };
    std::vector<WorkstationLaserRangingData> workstationLaserRangingDatas;
    std::vector<::PlcClientConfigure::WorkstationLaserRanging> workstationLaserRangings;

    workstationLaserRangingDatas.reserve(configure.workstationLaserRangings.size());
    workstationLaserRangings.reserve(configure.workstationLaserRangings.size());

    for (const auto &[
        modbusChannelIndex,
        workstationControlAAddress,
        workstationControlBAddress,
        statusAAddress,
        positionBAddress,
        horizontalPositionSize,
        referenceValue,
        lowerLimitValue,
        upperLimitValue,
        sensorSize
        ] : configure.workstationLaserRangings) {
        workstationLaserRangings.push_back({
            modbusChannelIndex,
            workstationControlAAddress,
            workstationControlBAddress,
            statusAAddress,
            positionBAddress,
            horizontalPositionSize,
            referenceValue,
            lowerLimitValue,
            upperLimitValue,
            static_cast<PlcClientConfigure::WorkstationLaserRanging::SensorSize>(sensorSize),
        });
    }
    plcClientConfigure.workstationLaserRangings = workstationLaserRangings.data();
    plcClientConfigure.workstationLaserRangingsSize = workstationLaserRangings.size();

    // 2D工位
    struct Workstation2DData {
        struct PositionGroupData {
            std::vector<::PlcClientConfigure::Workstation2D::PositionGroup::Position> positions;
        };
        std::vector<PositionGroupData> positionGroupDatas;
        std::vector<::PlcClientConfigure::Workstation2D::PositionGroup> positionGroups;
    };
    std::vector<Workstation2DData> workstation2DDatas;
    std::vector<::PlcClientConfigure::Workstation2D> workstation2Ds;

    workstation2DDatas.reserve(configure.workstation2Ds.size());
    workstation2Ds.reserve(configure.workstation2Ds.size());

    for (const auto &[
        modbusChannelIndex,
        workstationControlAAddress,
        workstationControlBAddress,
        positionGroupBAddress,
        horizontalPositionSize,
        positionGroups
        ] : configure.workstation2Ds) {
        workstation2DDatas.push_back({});
        for (const auto &[
            lightMask,
            lightCoordinates,
            positions
            ] : positionGroups) {
            workstation2DDatas.back().positionGroupDatas.emplace_back();
            for (const auto &[
                holdDuration,
                cameraMask,
                cameraCoordinates,
                cameraChannelTypes,
                cameraImageNumbers
                ]: positions) {
                workstation2DDatas.back().positionGroupDatas.back().positions.push_back({
                    holdDuration,
                    cameraMask,
                    {},
                    {}
                });
                std::copy(cameraCoordinates.begin(),
                          cameraCoordinates.end(),
                          workstation2DDatas.back().positionGroupDatas.back().positions.back().cameraCoordinates
                );
                std::transform(cameraChannelTypes.begin(), cameraChannelTypes.end(), workstation2DDatas.back().positionGroupDatas.back().positions.back().cameraChannelTypes, [](Configure::Workstation2D::PositionGroup::Position::CameraChannelType cameraChannelType)
                {
                    return static_cast<PlcClientConfigure::Workstation2D::PositionGroup::Position::CameraChannelType>(cameraChannelType);
                });
            }
            workstation2DDatas.back().positionGroups.push_back({
                lightMask,
                {},
                workstation2DDatas.back().positionGroupDatas.back().positions.data(),
                workstation2DDatas.back().positionGroupDatas.back().positions.size()
            });
            std::copy(lightCoordinates.begin(),
                      lightCoordinates.end(),
                      workstation2DDatas.back().positionGroups.back().lightCoordinates
            );
        }

        workstation2Ds.push_back({
            static_cast<size_t>(modbusChannelIndex),
            workstationControlAAddress,
            workstationControlBAddress,
            positionGroupBAddress,
            horizontalPositionSize,
            workstation2DDatas.back().positionGroups.data(),
            workstation2DDatas.back().positionGroupDatas.size()
        });
    }
    plcClientConfigure.workstation2Ds = workstation2Ds.data();
    plcClientConfigure.workstation2DsSize = workstation2Ds.size();

    // 3D工位
    struct Workstation3DData {
        std::vector<::PlcClientConfigure::Workstation3D::Position> positions;
    };
    std::vector<Workstation3DData> workstation3DDatas;
    std::vector<::PlcClientConfigure::Workstation3D> workstation3Ds;

    workstation3DDatas.reserve(configure.workstation3Ds.size());
    workstation3Ds.reserve(configure.workstation3Ds.size());

    for (const auto &[
        modbusChannelIndex,
        workstationControlAAddress,
        workstationControlBAddress,
        positionBAddress,
        horizontalPositionSize,
        positions
        ] : configure.workstation3Ds) {
        workstation3DDatas.emplace_back();
        for (const auto &[
            zAxisMask,
            zAxisCoordinates
            ] : positions) {
            workstation3DDatas.back().positions.push_back({
                zAxisMask,
                {}
            });
            std::copy(zAxisCoordinates.begin(),
                      zAxisCoordinates.end(),
                      workstation3DDatas.back().positions.back().zAxisCoordinates
            );
        }
        workstation3Ds.push_back({
            static_cast<size_t>(modbusChannelIndex),
            workstationControlAAddress,
            workstationControlBAddress,
            positionBAddress,
            horizontalPositionSize,
            workstation3DDatas.back().positions.data(),
            workstation3DDatas.back().positions.size()
        });
    }
    plcClientConfigure.workstation3Ds = workstation3Ds.data();
    plcClientConfigure.workstation3DsSize = workstation3Ds.size();

    // 下料位
    struct WorkstationUnloadData {
    };
    std::vector<WorkstationUnloadData> workstationUnloadDatas;
    std::vector<::PlcClientConfigure::WorkstationUnload> workstationUnloads;

    workstationUnloadDatas.reserve(configure.workstationUnloads.size());
    workstationUnloads.reserve(configure.workstationUnloads.size());

    for (const auto &[
        modbusChannelIndex,
        workstationControlAAddress,
        workstationControlBAddress,
        sortResultAAddress,
        resultBAddress
        ] : configure.workstationUnloads) {
        workstationUnloads.push_back({
            modbusChannelIndex,
            workstationControlAAddress,
            workstationControlBAddress,
            sortResultAAddress,
            resultBAddress
        });
    }
    plcClientConfigure.workstationUnloads = workstationUnloads.data();
    plcClientConfigure.workstationUnloadsSize = workstationUnloads.size();

    // 报警
    struct Alarm {
    } alarm;

    plcClientConfigure.alarm = {
        configure.alarm.modbusChannelIndex,
        configure.alarm.alarmInfoAAddress
    };

    return plcclient_start(plcClientConfigure);
}

void PlcClientCppAdapter::stop() {
    plcclient_stop();
}

std::string PlcClientCppAdapter::getVersion() {
    return {plcclient_getVersion()};
}
