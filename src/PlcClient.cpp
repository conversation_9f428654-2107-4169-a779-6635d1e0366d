#include "PlcClient.h"

#include "Version.hpp"
#include "Logger.hpp"
#include "ModbusTcpMaster.hpp"

#include "System.hpp"
#include "WorkstationLaserRanging.hpp"
#include "Workstation2D.hpp"
#include "Workstation3D.hpp"
#include "WorkstationUnload.hpp"
#include "Alarm.hpp"

#include <shared_mutex>
#include <ranges>

namespace PlcClient {

    static class Initializer {
    public:
        Initializer() {
            // 预热时区转换
            Util::now();
        }
    } initializer;

    static std::mutex startMutex;

    static std::vector<std::shared_ptr<ModbusTcpMaster>> modbusTcpMasters;

    static std::shared_ptr<System> system = nullptr;
    static std::vector<std::shared_ptr<WorkstationLaserRanging>> workstationLaserRangings;
    static std::vector<std::shared_ptr<Workstation2D>> workstation2Ds;
    static std::vector<std::shared_ptr<Workstation3D>> workstation3Ds;
    static std::vector<std::shared_ptr<WorkstationUnload>> workstationUnloads;
    static std::shared_ptr<Alarm> alarm = nullptr;

    static std::function<void(size_t modbusChannelIndex, bool connected)> modbusConnectionStateChangedCallback;
    static std::shared_mutex modbusConnectionStateChangedCallbackMutex;

    static std::function<void(PlcClientRunningState runningState)> runningStateChangedCallback;
    static std::shared_mutex runningStateChangedCallbackMutex;

    static std::function<void(uint16_t stateWord)> stateWordChangedCallback;
    static std::shared_mutex stateWordChangedCallbackMutex;

    static std::function<void(const bool *alarmState)> alarmStateChangedCallback;
    static std::shared_mutex alarmStateChangedCallbackMutex;

    static std::function<void(size_t workstation2DIndex, const char *trayCode)> workstationStarted2DCallback;
    static std::shared_mutex workstationStarted2DCallbackMutex;

    static std::function<void(size_t workstation2DIndex, size_t horizontalPositionIndex, size_t positionGroupIndex)> positionGroupStarted2DCallback;
    static std::shared_mutex positionGroupStarted2DCallbackMutex;

    static std::function<void(size_t workstation2DIndex, size_t horizontalPositionIndex, size_t positionGroupIndex, bool &result)> queryPositionGroupResult2DCallback;
    static std::shared_mutex queryPositionGroupResult2DCallbackMutex;

    static std::function<void(size_t workstation2DIndex)> workstationFinished2DCallback;
    static std::shared_mutex workstationFinished2DCallbackMutex;

    static std::function<void(size_t workstation3DIndex, const char *trayCode)> workstationStarted3DCallback;
    static std::shared_mutex workstationStarted3DCallbackMutex;

    static std::function<void(size_t workstation3DIndex, ptrdiff_t &horizontalPositionIndex, size_t &positionIndex)> positionStarted3DCallback;
    static std::shared_mutex positionStarted3DCallbackMutex;

    static std::function<void(size_t workstation3DIndex, size_t horizontalPositionIndex, size_t positionIndex)> positionArrived3DCallback;
    static std::shared_mutex positionArrived3DCallbackMutex;

    static std::function<void(size_t workstation3DIndex)> workstationFinished3DCallback;
    static std::shared_mutex workstationFinished3DCallbackMutex;

    static std::function<void(size_t workstationUnloadIndex, const char *trayCode, uint8_t *result, uint16_t *mould)> queryResultCallback;
    static std::shared_mutex queryResultCallbackMutex;

    static std::function<void(size_t workstationUnloadIndex, const char *trayCode, const SortResult *sortResult, size_t sortResultSize, bool &result)> sortResultCallback;
    static std::shared_mutex sortResultCallbackMutex;

    static std::atomic<bool> disableLaserRanging = false;

}

bool plcclient_start(const PlcClientConfigure &plcClientConfigure) {
    std::lock_guard startLock(PlcClient::startMutex);

    if (!PlcClient::modbusTcpMasters.empty()) {
        Logger::log(Logger::ERR, "不能重复启动");
        return false;
    }

    // 检查Modbus连接参数
    if (plcClientConfigure.modbusChannelsSize == 0) {
        Logger::log(Logger::ERR, "Modbus 连接数量不能为0");
        return false;
    }

    for (int i=0;i<plcClientConfigure.modbusChannelsSize;i++) {
        if (plcClientConfigure.modbusChannels[i].ip == nullptr) {
            Logger::log(Logger::ERR, std::format("序号为{}的Modbus连接 IP地址为空", i));
            return false;
        }
    }

    // 检查各工位的 Modbus 连接序号
    if (plcClientConfigure.system.modbusChannelIndex >= plcClientConfigure.modbusChannelsSize) {
        Logger::log(Logger::ERR, "系统 指定的Modbus连接序号错误");
        return false;
    }
    for (int i=0;i<plcClientConfigure.workstationLaserRangingsSize;i++) {
        if (plcClientConfigure.workstationLaserRangings[i].modbusChannelIndex >= plcClientConfigure.modbusChannelsSize) {
            Logger::log(Logger::ERR, std::format("激光测距工位{} 指定的Modbus连接序号错误", i+1));
            return false;
        }
    }
    for (int i=0;i<plcClientConfigure.workstation2DsSize;i++) {
        if (plcClientConfigure.workstation2Ds[i].modbusChannelIndex >= plcClientConfigure.modbusChannelsSize) {
            Logger::log(Logger::ERR, std::format("2D工位{} 指定的Modbus连接错误", i+1));
            return false;
        }
    }
    for (int i=0;i<plcClientConfigure.workstation3DsSize;i++) {
        if (plcClientConfigure.workstation3Ds[i].modbusChannelIndex >= plcClientConfigure.modbusChannelsSize) {
            Logger::log(Logger::ERR, std::format("3D工位{} 指定的Modbus连接错误", i+1));
            return false;
        }
    }
    for (int i=0;i<plcClientConfigure.workstationUnloadsSize;i++) {
        if (plcClientConfigure.workstationUnloads[i].modbusChannelIndex >= plcClientConfigure.modbusChannelsSize) {
            Logger::log(Logger::ERR, std::format("下料位{} 指定的Modbus连接错误", i+1));
            return false;
        }
    }
    if (plcClientConfigure.alarm.modbusChannelIndex >= plcClientConfigure.modbusChannelsSize) {
        Logger::log(Logger::ERR, "报警 指定的Modbus连接错误");
        return false;
    }

    // 产品编号不能为空
    if (plcClientConfigure.system.productCode == nullptr) {
        Logger::log(Logger::ERR, "系统 中的产品编号为空");
        return false;
    }
    // 产品编号的长度不能大于60
    if (std::strlen(plcClientConfigure.system.productCode) > 60) {
        Logger::log(Logger::ERR, "系统 中的产品编号长度超过了60");
        return false;
    }

    // 2D工位中，每组的工位数量不应超过10
    for (int i=0;i<plcClientConfigure.workstation2DsSize;i++) {
        for (int j=0;j<plcClientConfigure.workstation2Ds[i].positionGroupsSize;j++) {
            if (plcClientConfigure.workstation2Ds[i].positionGroups[j].positionsSize > 10) {
                Logger::log(Logger::ERR, std::format("2D工位{} 点位组{}中包含的点位数量超过了10", i+1, j));
                return false;
            }
        }
    }

    // 构造 ModbusTCP 主站对象
    for (int i=0;i<plcClientConfigure.modbusChannelsSize;i++) {
        PlcClient::modbusTcpMasters.push_back(std::make_shared<ModbusTcpMaster>(
            plcClientConfigure.modbusChannels[i].ip,
            plcClientConfigure.modbusChannels[i].port,
            [&, i](const bool connected) {
                std::shared_lock lock(PlcClient::modbusConnectionStateChangedCallbackMutex);
                if (PlcClient::modbusConnectionStateChangedCallback) {
                    PlcClient::modbusConnectionStateChangedCallback(i, connected);
                }
            }
        ));
    }

    // 构造系统对象
    PlcClient::system = std::make_shared<System>(
        "系统",
        *PlcClient::modbusTcpMasters[plcClientConfigure.system.modbusChannelIndex],
        ConfigureSystem {
            plcClientConfigure.system.systemAAddress,
            plcClientConfigure.system.systemBAddress,
            plcClientConfigure.system.productCode
        },
        [&](System::RunningState runningState) {
            std::shared_lock lock(PlcClient::runningStateChangedCallbackMutex);
            if (PlcClient::runningStateChangedCallback) {
                PlcClient::runningStateChangedCallback(static_cast<PlcClientRunningState>(runningState));
            }
        },
        [&](const std::uint16_t stateWord) {
            if (bool disableLaserRanging = stateWord >> 4 & 1; disableLaserRanging != PlcClient::disableLaserRanging.load()) {
                PlcClient::disableLaserRanging.store(disableLaserRanging);
                Logger::log(Logger::INFO, std::format("停用激光测距:{}", disableLaserRanging));
            }

            std::shared_lock lock(PlcClient::stateWordChangedCallbackMutex);
            if (PlcClient::stateWordChangedCallback) {
                PlcClient::stateWordChangedCallback(stateWord);
            }
        }
    );

    // 构造激光测距工位对象
    for (int i=0;i<plcClientConfigure.workstationLaserRangingsSize; i++) {
        ConfigureLaserRanging configureLaserRanging {
            plcClientConfigure.workstationLaserRangings[i].workstationControlAAddress,
            plcClientConfigure.workstationLaserRangings[i].workstationControlBAddress,
            plcClientConfigure.workstationLaserRangings[i].statusAAddress,
            plcClientConfigure.workstationLaserRangings[i].positionBAddress,
            plcClientConfigure.workstationLaserRangings[i].horizontalPositionSize,
            plcClientConfigure.workstationLaserRangings[i].referenceValue,
            plcClientConfigure.workstationLaserRangings[i].lowerLimitValue,
            plcClientConfigure.workstationLaserRangings[i].upperLimitValue,
            static_cast<ConfigureLaserRanging::SensorSize>(plcClientConfigure.workstationLaserRangings[i].sensorSize),
        };

        PlcClient::workstationLaserRangings.push_back(std::make_shared<WorkstationLaserRanging>(
            std::format("激光测距工位{}",i+1),
            *PlcClient::modbusTcpMasters[plcClientConfigure.workstationLaserRangings[i].modbusChannelIndex],
            ZOffsetManager::getInstance(),
            configureLaserRanging,
            PlcClient::disableLaserRanging)
        );
    }

    // 构造2D工位对象
    for (int i=0;i<plcClientConfigure.workstation2DsSize; i++) {
        Configure2D configure2D{
            plcClientConfigure.workstation2Ds[i].workstationControlAAddress,
            plcClientConfigure.workstation2Ds[i].workstationControlBAddress,
            plcClientConfigure.workstation2Ds[i].positionGroupBAddress,
            plcClientConfigure.workstation2Ds[i].horizontalPositionSize
        };

        for (int j=0;j<plcClientConfigure.workstation2Ds[i].positionGroupsSize;j++) {
            configure2D.positionGroups.emplace_back(Configure2D::PositionGroup {
                plcClientConfigure.workstation2Ds[i].positionGroups[j].lightMask
            });
            std::ranges::copy(plcClientConfigure.workstation2Ds[i].positionGroups[j].lightCoordinates,
                              configure2D.positionGroups.back().lightCoordinates.begin());
            for (int k=0;k<plcClientConfigure.workstation2Ds[i].positionGroups[j].positionsSize;k++) {
                configure2D.positionGroups.back().positions.push_back({
                    plcClientConfigure.workstation2Ds[i].positionGroups[j].positions[k].holdDuration,
                    plcClientConfigure.workstation2Ds[i].positionGroups[j].positions[k].cameraMask
                });
                std::ranges::copy(plcClientConfigure.workstation2Ds[i].positionGroups[j].positions[k].cameraCoordinates,
                                  configure2D.positionGroups.back().positions.back().cameraCoordinates.begin());
                std::ranges::copy(plcClientConfigure.workstation2Ds[i].positionGroups[j].positions[k].cameraChannelTypes |
                                  std::views::transform([](PlcClientConfigure::Workstation2D::PositionGroup::Position::CameraChannelType cameraChannelType) {
                                      return static_cast<Configure2D::PositionGroup::Position::CameraChannelType>(cameraChannelType);
                                  }),
                                  configure2D.positionGroups.back().positions.back().cameraChannelTypes.begin());
            }
        }

        PlcClient::workstation2Ds.push_back(std::make_shared<Workstation2D>(
            std::format("2D工位{}",i+1),
            *PlcClient::modbusTcpMasters[plcClientConfigure.workstation2Ds[i].modbusChannelIndex],
            ZOffsetManager::getInstance(),
            configure2D,
            PlcClient::disableLaserRanging,
            [&, i](const std::string &trayCode) {
                std::shared_lock lock(PlcClient::workstationStarted2DCallbackMutex);
                if (PlcClient::workstationStarted2DCallback) {
                    PlcClient::workstationStarted2DCallback(i, trayCode.c_str());
                }
            },
            [&, i](const std::size_t horizontalPositionIndex, const std::size_t positionGroupIndex) {
                std::shared_lock lock(PlcClient::positionGroupStarted2DCallbackMutex);
                if (PlcClient::positionGroupStarted2DCallback) {
                    PlcClient::positionGroupStarted2DCallback(i, horizontalPositionIndex, positionGroupIndex);
                }
            },
            [&, i](const std::size_t horizontalPositionIndex, const std::size_t positionGroupIndex, bool &result) {
                std::shared_lock lock(PlcClient::queryPositionGroupResult2DCallbackMutex);
                if (PlcClient::queryPositionGroupResult2DCallback) {
                    PlcClient::queryPositionGroupResult2DCallback(i, horizontalPositionIndex, positionGroupIndex, result);
                }
            },
            [&, i] {
                std::shared_lock lock(PlcClient::workstationFinished2DCallbackMutex);
                if (PlcClient::workstationFinished2DCallback) {
                    PlcClient::workstationFinished2DCallback(i);
                }
            }
        ));
    }

    // 构建3D工位对象
    for (int i=0;i<plcClientConfigure.workstation3DsSize; i++) {
        Configure3D configure3D {
            plcClientConfigure.workstation3Ds[i].workstationControlAAddress,
            plcClientConfigure.workstation3Ds[i].workstationControlBAddress,
            plcClientConfigure.workstation3Ds[i].positionBAddress,
            plcClientConfigure.workstation3Ds[i].horizontalPositionSize
        };

        for (int j=0;j<plcClientConfigure.workstation3Ds[i].positionsSize;j++) {
            configure3D.positions.push_back({
                plcClientConfigure.workstation3Ds[i].positions[j].zAxisMask
            });
            std::ranges::copy(plcClientConfigure.workstation3Ds[i].positions[j].zAxisCoordinates,
                              configure3D.positions.back().zAxisCoordinates.begin());
        }

        PlcClient::workstation3Ds.push_back(std::make_shared<Workstation3D>(
            std::format("3D工位{}",i+1),
            *PlcClient::modbusTcpMasters[plcClientConfigure.workstation3Ds[i].modbusChannelIndex],
            configure3D,
            [&, i](const std::string &trayCode) {
                std::shared_lock lock(PlcClient::workstationStarted3DCallbackMutex);
                if (PlcClient::workstationStarted3DCallback) {
                    PlcClient::workstationStarted3DCallback(i, trayCode.c_str());
                }
            },
            [&, i](std::ptrdiff_t &horizontalPositionIndex, std::size_t &positionIndex) {
                std::shared_lock lock(PlcClient::positionStarted3DCallbackMutex);
                if (PlcClient::positionStarted3DCallback) {
                    PlcClient::positionStarted3DCallback(i, horizontalPositionIndex, positionIndex);
                }
            },
            [&, i](std::size_t horizontalPositionIndex, std::size_t positionIndex) {
                std::shared_lock lock(PlcClient::positionArrived3DCallbackMutex);
                if (PlcClient::positionArrived3DCallback) {
                    PlcClient::positionArrived3DCallback(i, horizontalPositionIndex, positionIndex);
                }
            },
            [&, i] {
                std::shared_lock lock(PlcClient::workstationFinished3DCallbackMutex);
                if (PlcClient::workstationFinished3DCallback) {
                    PlcClient::workstationFinished3DCallback(i);
                }
            }
        ));
    }

    // 构造下料工位对象
    for (int i=0;i<plcClientConfigure.workstationUnloadsSize;i++) {
        ConfigureUnload configureUnload {
            plcClientConfigure.workstationUnloads[i].workstationControlAAddress,
            plcClientConfigure.workstationUnloads[i].workstationControlBAddress,
            plcClientConfigure.workstationUnloads[i].sortResultAAddress,
            plcClientConfigure.workstationUnloads[i].resultBAddress
        };

        PlcClient::workstationUnloads.push_back(std::make_shared<WorkstationUnload>(
            std::format("下料工位{}",i+1),
            *PlcClient::modbusTcpMasters[plcClientConfigure.workstationUnloads[i].modbusChannelIndex],
            ZOffsetManager::getInstance(),
            configureUnload,
            PlcClient::disableLaserRanging,
            [&, i](const std::string &trayCode) {
                std::shared_lock lock(PlcClient::queryResultCallbackMutex);
                WorkstationUnload::Result result {};
                if (PlcClient::queryResultCallback) {
                    PlcClient::queryResultCallback(i, trayCode.c_str(), result.result.data(), &result.mould);
                }
                return result;
            },
            [&, i](const std::string &trayCode, const std::vector<WorkstationUnload::SortResult>& sortResults) {
                std::shared_lock lock(PlcClient::sortResultCallbackMutex);
                bool result {};
                std::vector<SortResult> sortResultData;
                for (const auto & [trayType, trayIndex, holeIndex] : sortResults) {
                    sortResultData.push_back({
                        trayType,
                        trayIndex,
                        holeIndex
                    });
                }
                if (PlcClient::sortResultCallback) {
                    PlcClient::sortResultCallback(i, trayCode.c_str(), sortResultData.data(), sortResultData.size(), result);
                }

                return result;
            }));
    }

    // 构建报警对象
    PlcClient::alarm = std::make_shared<Alarm>(
        "报警",
        *PlcClient::modbusTcpMasters[plcClientConfigure.alarm.modbusChannelIndex],
        ConfigureAlarm {
            plcClientConfigure.alarm.alarmInfoAAddress
        },
        [&](const std::array<bool, 160>& alarms) {
            std::shared_lock lock(PlcClient::alarmStateChangedCallbackMutex);
            if (PlcClient::alarmStateChangedCallback) {
                PlcClient::alarmStateChangedCallback(alarms.data());
            }
        }
    );

    return true;
}

void plcclient_stop() {
    std::lock_guard startLock(PlcClient::startMutex);

    PlcClient::system.reset();
    PlcClient::workstationLaserRangings.clear();
    PlcClient::workstation2Ds.clear();
    PlcClient::workstation3Ds.clear();
    PlcClient::workstationUnloads.clear();
    PlcClient::alarm.reset();

    PlcClient::modbusTcpMasters.clear();
    ZOffsetManager::getInstance().clear();
}

void plcclient_setModbusConnectionStateChangedCallback(void (*callback)(size_t modbusChannelIndex, bool connected)) {
    std::unique_lock lock(PlcClient::modbusConnectionStateChangedCallbackMutex);
    PlcClient::modbusConnectionStateChangedCallback = [callback](const size_t modbusChannelIndex, const bool connected){
        Logger::log(Logger::DEBUG, std::format("通知中控Modbus连接状态改变 连接序号:{} 状态:{}", modbusChannelIndex, connected));
        callback(modbusChannelIndex, connected);
    };
}

void plcclient_setRunningStateChangedCallback(void(*callback)(PlcClientRunningState runningState)) {
    std::unique_lock lock(PlcClient::runningStateChangedCallbackMutex);
    PlcClient::runningStateChangedCallback = [callback](const PlcClientRunningState runningState) {
        Logger::log(Logger::DEBUG, std::format("通知中控运行状态改变 运行状态:{}", static_cast<std::uint16_t>(runningState)));
        callback(runningState);
    };
}

void plcclient_setStateWordChangedCallback(void(*callback)(uint16_t stateWord)) {
    std::unique_lock lock(PlcClient::stateWordChangedCallbackMutex);
    PlcClient::stateWordChangedCallback = [callback](const uint16_t stateWord) {
        Logger::log(Logger::DEBUG, std::format("通知中控状态字改变 状态字:{}", stateWord));
        callback(stateWord);
    };
}

void plcclient_setAlarmStateChangedCallback(void(*callback)(const bool *alarmState)) {
    std::unique_lock lock(PlcClient::alarmStateChangedCallbackMutex);
    PlcClient::alarmStateChangedCallback = [callback](const bool *alarmState) {
        Logger::log(Logger::DEBUG, "通知中控报警状态改变");
        callback(alarmState);
    };
}

void plcclient_setWorkstationStarted2DCallback(void(*callback)(size_t workstation2DIndex, const char *trayCode)) {
    std::unique_lock lock(PlcClient::workstationStarted2DCallbackMutex);
    PlcClient::workstationStarted2DCallback = [callback](const size_t workstation2DIndex, const char *trayCode) {
        Logger::log(Logger::DEBUG, std::format("通知中控2D工位工位开始 2D工位序号:{} 托盘号:{}", workstation2DIndex, trayCode));
        callback(workstation2DIndex, trayCode);
    };
}

void plcclient_setPositionGroupStarted2DCallback(void(*callback)(size_t workstation2DIndex, size_t horizontalPositionIndex, size_t positionGroupIndex)) {
    std::unique_lock lock(PlcClient::positionGroupStarted2DCallbackMutex);
    PlcClient::positionGroupStarted2DCallback = [callback](const size_t workstation2DIndex, const size_t horizontalPositionIndex, const size_t positionGroupIndex) {
        Logger::log(Logger::INFO, std::format("通知中控2D工位点位开始 2D工位序号:{} 水平点位序号:{} 点位组序号:{}", workstation2DIndex, horizontalPositionIndex, positionGroupIndex));
        callback(workstation2DIndex, horizontalPositionIndex, positionGroupIndex);
    };
}

void plcclient_setQueryPositionGroupResult2DCallback(void(*callback)(size_t workstation2DIndex, size_t horizontalPositionIndex, size_t positionGroupIndex, bool &result)) {
    std::unique_lock lock(PlcClient::queryPositionGroupResult2DCallbackMutex);
    PlcClient::queryPositionGroupResult2DCallback = [callback](const size_t workstation2DIndex, size_t horizontalPositionIndex, const size_t positionGroupIndex, bool &result) {
        Logger::log(Logger::INFO, std::format("通知中控2D工位点位到达 2D工位序号:{} 水平点位序号:{} 点位组序号:{}", workstation2DIndex, horizontalPositionIndex, positionGroupIndex));
        callback(workstation2DIndex, horizontalPositionIndex, positionGroupIndex, result);
        Logger::log(Logger::INFO, std::format("中控反馈2D工位点位收图结果:{} 2D工位序号:{} 水平点位序号:{} 点位序号:{}", result, workstation2DIndex, horizontalPositionIndex, positionGroupIndex));
    };
}

void plcclient_setWorkstationFinished2DCallback(void(*callback)(size_t workstation2DIndex)) {
    std::unique_lock lock(PlcClient::workstationFinished2DCallbackMutex);
    PlcClient::workstationFinished2DCallback = [callback](const size_t workstation2DIndex) {
        Logger::log(Logger::INFO, std::format("通知中控2D工位结束 2D工位序号:{}", workstation2DIndex));
        callback(workstation2DIndex);
    };
}

void plcclient_setWorkstationStarted3DCallback(void(*callback)(size_t workstation3DIndex, const char *trayCode)) {
    std::unique_lock lock(PlcClient::workstationStarted3DCallbackMutex);
    PlcClient::workstationStarted3DCallback = [callback](const size_t workstation3DIndex, const char *trayCode) {
        Logger::log(Logger::DEBUG, std::format("通知中控3D工位 工位开始 3D工位序号:{} 托盘号:{}", workstation3DIndex, trayCode));
        callback(workstation3DIndex, trayCode);
    };
}

void plcclient_setPositionStarted3DCallback(void(*callback)(size_t workstation3DIndex, ptrdiff_t &horizontalPositionIndex, size_t &positionIndex)) {
    std::unique_lock lock(PlcClient::positionStarted3DCallbackMutex);
    PlcClient::positionStarted3DCallback = [callback](const size_t workstation3DIndex, ptrdiff_t &horizontalPositionIndex, size_t &positionIndex) {
        Logger::log(Logger::INFO, std::format("请求中控下发3D工位本次点位 3D工位序号:{}", workstation3DIndex));
        callback(workstation3DIndex, horizontalPositionIndex, positionIndex);
        Logger::log(Logger::INFO, std::format("中控下发3D工位本次点位: 3D工位序号:{} 水平点位序号:{} 点位序号:{}", workstation3DIndex, horizontalPositionIndex, positionIndex));
    };
}

void plcclient_setPositionArrived3DCallback(void(*callback)(size_t workstation3DIndex, size_t horizontalPositionIndex, size_t positionIndex)) {
    std::unique_lock lock(PlcClient::positionArrived3DCallbackMutex);
    PlcClient::positionArrived3DCallback = [callback](const size_t workstation3DIndex, const size_t horizontalPositionIndex, const size_t positionIndex) {
        Logger::log(Logger::INFO, std::format("通知中控3D工位点位已到达 3D工位序号:{} 水平点位序号:{} 点位序号:{}", workstation3DIndex, horizontalPositionIndex,positionIndex));
        callback(workstation3DIndex, horizontalPositionIndex, positionIndex);
    };
}

void plcclient_setWorkstationFinished3DCallback(void(*callback)(size_t workstation3DIndex)) {
    std::unique_lock lock(PlcClient::workstationFinished3DCallbackMutex);
    PlcClient::workstationFinished3DCallback = [callback](const size_t workstation3DIndex) {
        Logger::log(Logger::INFO, std::format("通知中控3D工位结束 3D工位序号:{}", workstation3DIndex));
        callback(workstation3DIndex);
    };
}

void plcclient_setWorkstationUnloadQueryResultCallback(void(*callback)(size_t workstationUnloadIndex, const char *trayCode, uint8_t *result, uint16_t *mould)) {
    std::unique_lock lock(PlcClient::queryResultCallbackMutex);
    PlcClient::queryResultCallback = [callback](const size_t workstationUnloadIndex, const char *trayCode, uint8_t *result, uint16_t *mould) {
        Logger::log(Logger::INFO, std::format("请求中控下发判定结果 托盘号:{} 下料位序号:{}", trayCode, workstationUnloadIndex));
        callback( workstationUnloadIndex,trayCode, result, mould);
        std::stringstream ss;
        for (std::size_t i=0;i<400;i++) {
            if (i%20==0) {
                ss << std::endl << std::format("[{:3}-{:3}] ",i+1, i+20);
            }
            ss << std::format("{},",result[i]);
            if ((i+1)%10==0) {
                ss << " ";
            }
        }
        Logger::log(Logger::INFO, std::format("中控下发判定结果 托盘号:{} 下料位序号:{} 模号:{} 判定结果:\n{}",
            trayCode,
            workstationUnloadIndex,
            *mould,
            ss.str()));
    };
}

const char * plcclient_getVersion() {
    return Version::getVersion().c_str();
}

void plcclient_setLogCallback(void(*callback)(PlcClientLogType logType, const char *log)) {
    Logger::setWriter([callback](const Logger::LogType &type, const std::string &message) {
        callback(static_cast<PlcClientLogType>(type), message.c_str());
    });
}

void plcclient_setWorkstationUnloadSortResultCallback(void(* callback)(size_t workstationUnloadIndex, const char* trayCode, const SortResult* sortResult, size_t sortResultSize,bool &result)) {
    std::unique_lock lock(PlcClient::sortResultCallbackMutex);
    PlcClient::sortResultCallback = [callback](size_t workstationUnloadIndex, const char *trayCode, const SortResult *sortResult, const size_t sortResultSize, bool &result) {
        std::stringstream ss;
        ss << "上传分拣结果 托盘号:" << trayCode << " 下料位序号:" << workstationUnloadIndex << " 工件数量:" << sortResultSize << " 分拣结果:" << std::endl;
        for (std::size_t i=0;i<sortResultSize;i++) {
            ss << "托盘类型:" << sortResult[i].trayType << " 托盘编号:" << sortResult[i].trayIndex << " 穴位编号:" << sortResult[i].holeIndex << std::endl;
        }

        callback( workstationUnloadIndex,trayCode, sortResult, sortResultSize, result);

        Logger::log(Logger::INFO, std::format("中控反馈分拣结果上传结果 托盘号:{} 下料位序号:{} 上传结果:{}",
            trayCode,
            workstationUnloadIndex,
            result));
    };
}
